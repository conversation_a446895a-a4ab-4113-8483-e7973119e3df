export interface PublicOrderStatus {
  shopifyOrderNumber: string;
  orderDate: string;
  orderStatus: string;
  statusUpdatedAt: string;
  customerFirstName: string;
  customerLastName: string;
  customerEmail: string;
  itemCount: number;
  lineItems: PublicLineItem[];
  shippingAddress?: {
    city: string;
    country: string;
    address1: string;
    address2?: string | null;
    province: string;
    country_code: string;
    province_code: string;
  };
}

export interface PublicLineItem {
  id: string;
  itemNumber: string;
  priority: string;
  quantity: number;
  status: string;
  currentStatus?: string;
  lastUpdatedAt?: string;
  rejectedImage?: {
    url: string;
    rejectionReason?: string;
  };
  selectedImage?: {
    url: string;
    message?: string;
  };
}

export interface RequestNewImageData {
  orderNumber: string;
  lineItemId: string;
  reason: string;
}

export type FormType = 'newImageRequest' | 'customerContactNeeded' | 'customerApproval';


export interface RejectedImageResponse {
  orderNumber: string;
  itemNumber: string;
  images: {
    id: string;
    filename: string;
    url: string;
    status: string;
    uploadedAt: string;
  }[];
}


export type OrderStatusType = 'unfulfilled' | 'partially fulfilled' | 'fulfilled';

export const ORDER_STATUS_LABELS: Record<OrderStatusType, string> = {
  'unfulfilled': 'Processing',
  'partially fulfilled': 'Partially Shipped',
  'fulfilled': 'Completed'
};

export const ORDER_STATUS_COLORS: Record<OrderStatusType, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
  'unfulfilled': 'warning',
  'partially fulfilled': 'info',
  'fulfilled': 'success'
};

export const LINE_ITEM_STATUS_LABELS: Record<string, string> = {
  'pending': 'Pending',
  'in_progress': 'In Progress',
  'completed': 'Completed',
  'shipped': 'Shipped',
  'cancelled': 'Cancelled',
  'Awaiting Customer Response': 'Awaiting Customer Response',
  'Line Item Received': 'Line Item Received',
  'Cutout Pro Requested': 'Cutout Pro Requested',
  'Crop Review': 'Crop Review',
  'Crop Needed': 'Crop Needed',
  'Ready for Vendor': 'Ready for Vendor',
  'Template Placement': 'Template Placement',
  'Artwork Needed': 'Artwork Needed'
};

export const LINE_ITEM_STATUS_COLORS: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
  'pending': 'default',
  'in_progress': 'info',
  'completed': 'success',
  'shipped': 'primary',
  'cancelled': 'error',
  'Awaiting Customer Response': 'warning',
  'Line Item Received': 'info',
  'Cutout Pro Requested': 'info',
  'Crop Review': 'info',
  'Crop Needed': 'warning',
  'Ready for Vendor': 'success',
  'Template Placement': 'info',
  'Artwork Needed': 'warning'
};
