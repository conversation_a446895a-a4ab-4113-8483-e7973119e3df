'use client';
import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Alert,
  Chip,
} from '@mui/material';
import { Inventory2, RadioButtonChecked } from '@mui/icons-material';
import PublicStatusChip from './PublicStatusChip';
import { PublicLineItem, FormType } from '@/types/public-order-status.types';

interface LineItemsTableProps {
  lineItems: PublicLineItem[];
  orderNumber: string;
  formType?: FormType;
}

const LineItemsTable: React.FC<LineItemsTableProps> = ({ lineItems, orderNumber, formType }) => {
  if (!lineItems || lineItems.length === 0) {
    return (
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items
          </Typography>
          <Alert severity="info">No items found for this order.</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={3}>
            <Inventory2 color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Order Line Items ({lineItems.length})
            </Typography>
          </Box>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Item #</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Priority</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                    Quantity{' '}
                    {orderData && !loading && (
                      <Box>
                        {formType === 'newImageRequest' ? (
                          <NewImageRequestView orderData={orderData} formType={formType} />
                        ) : formType === 'customerContactNeeded' && 'lineItems' in orderData ? (
                          <CustomerContactView orderData={orderData as PublicOrderStatus} />
                        ) : formType === 'customerApproval' && 'lineItems' in orderData ? (
                          <CustomerApprovalView orderData={orderData as PublicOrderStatus} />
                        ) : 'lineItems' in orderData ? (
                          <>
                            <OrderDetailsCard orderData={orderData as PublicOrderStatus} />
                            <LineItemsTable
                              lineItems={(orderData as PublicOrderStatus).lineItems}
                              orderNumber={(orderData as PublicOrderStatus).shopifyOrderNumber}
                              formType={formType}
                            />
                          </>
                        ) : null}
                      </Box>
                    )}
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      Current Status
                    </Box>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {lineItems.map(item => (
                  <TableRow key={item.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {item.itemNumber || item.id.slice(-8)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={item.priority || 'Standard'}
                        size="small"
                        color={
                          item.priority === 'Pajama Rush'
                            ? 'warning'
                            : item.priority === 'Standard'
                              ? 'error'
                              : 'default'
                        }
                        variant="filled"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{item.quantity}</Typography>
                    </TableCell>
                    <TableCell>
                      <PublicStatusChip
                        status={item.currentStatus || item.status}
                        variant="lineItem"
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </>
  );
};

export default LineItemsTable;
