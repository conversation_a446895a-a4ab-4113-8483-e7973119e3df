'use server'

import { PublicOrderStatus, RejectedImageResponse } from '@/types/public-order-status.types';
import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL;
const accessToken = process.env.NEXT_PUBLIC_CUSTOMER_VALIDATION_TOKEN;

function createPublicApiClient() {
  return axios.create({
    baseURL: API_URL,
    withCredentials: false,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
}

// Common error handler for API responses
function handleApiError(error: any, context: string): never {
  console.error(`Error in ${context}:`, error);

  if (axios.isAxiosError(error)) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      throw new Error('Invalid access. Please use the link from your shopify order tracking.');
    }
    if (error.response?.status === 404) {
      throw new Error(context.includes('rejected image')
        ? 'No rejected image found for this order'
        : 'Order not found. Please check your order number and email.');
    }
    if (error.response?.status && error.response.status >= 500) {
      throw new Error('Server error. Please try again later.');
    }
    throw new Error(error.response?.data?.message || `Failed to ${context.toLowerCase()}`);
  }

  throw new Error(`Unable to ${context.toLowerCase()}. Please check your connection and try again.`);
}

export async function fetchPublicOrderStatus(
  orderNumber: string,
  customerEmail?: string,
): Promise<PublicOrderStatus | RejectedImageResponse> {
  try {
    const publicApiClient = createPublicApiClient();
    const requestBody = {
      orderNumber: orderNumber,
      email: customerEmail || '',
    };
    const response = await publicApiClient.post('/order-tracking', requestBody);
    if (!response.data) {
      throw new Error('No data returned from server');
    }
    return response.data;
  } catch (error) {
    handleApiError(error, 'fetch order status');
  }
}
